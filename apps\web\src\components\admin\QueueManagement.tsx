import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Queue, 
  Play, 
  Pause, 
  Square,
  RefreshCw,
  AlertTriangle,
  CheckCircle,
  Clock,
  Zap,
  BarChart3,
  Settings,
  Trash2,
  RotateCcw,
  FastForward,
  Activity
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { HStack, VStack } from '@/components/ui/layout';
import { Typography } from '@/components/ui/typography';
import { formatDistanceToNow } from 'date-fns';

interface QueueJob {
  id: string;
  type: 'transformation' | 'analysis' | 'export' | 'cleanup';
  status: 'pending' | 'running' | 'completed' | 'failed' | 'retrying' | 'cancelled';
  priority: 'low' | 'normal' | 'high' | 'critical';
  created_at: string;
  started_at?: string;
  completed_at?: string;
  progress: number;
  retry_count: number;
  max_retries: number;
  error_message?: string;
  metadata: {
    user_id?: string;
    session_id?: string;
    estimated_duration?: number;
    resource_usage?: {
      cpu: number;
      memory: number;
    };
  };
}

interface QueueStats {
  total_jobs: number;
  pending_jobs: number;
  running_jobs: number;
  completed_jobs: number;
  failed_jobs: number;
  avg_processing_time: number;
  throughput_per_hour: number;
  error_rate: number;
}

interface QueueManagementProps {
  className?: string;
}

const JOB_TYPES = [
  { value: 'transformation', label: 'Code Transformation', icon: <Zap className="w-4 h-4" /> },
  { value: 'analysis', label: 'Code Analysis', icon: <BarChart3 className="w-4 h-4" /> },
  { value: 'export', label: 'Data Export', icon: <RefreshCw className="w-4 h-4" /> },
  { value: 'cleanup', label: 'Cleanup Task', icon: <Trash2 className="w-4 h-4" /> }
];

const PRIORITY_COLORS = {
  low: 'text-gray-500',
  normal: 'text-blue-500',
  high: 'text-orange-500',
  critical: 'text-red-500'
};

export const QueueManagement: React.FC<QueueManagementProps> = ({ className }) => {
  const [jobs, setJobs] = useState<QueueJob[]>([]);
  const [stats, setStats] = useState<QueueStats>({
    total_jobs: 0,
    pending_jobs: 0,
    running_jobs: 0,
    completed_jobs: 0,
    failed_jobs: 0,
    avg_processing_time: 0,
    throughput_per_hour: 0,
    error_rate: 0
  });
  const [isLoading, setIsLoading] = useState(false);
  const [selectedStatus, setSelectedStatus] = useState<string>('all');
  const [selectedType, setSelectedType] = useState<string>('all');
  const [queuePaused, setQueuePaused] = useState(false);

  // Fetch queue data
  const fetchQueueData = async () => {
    setIsLoading(true);
    try {
      // Mock data for demonstration
      const mockJobs: QueueJob[] = [
        {
          id: 'job_1',
          type: 'transformation',
          status: 'running',
          priority: 'high',
          created_at: new Date(Date.now() - 300000).toISOString(),
          started_at: new Date(Date.now() - 120000).toISOString(),
          progress: 65,
          retry_count: 0,
          max_retries: 3,
          metadata: {
            user_id: 'user123',
            session_id: 'sess_abc',
            estimated_duration: 180,
            resource_usage: { cpu: 45, memory: 512 }
          }
        },
        {
          id: 'job_2',
          type: 'analysis',
          status: 'pending',
          priority: 'normal',
          created_at: new Date(Date.now() - 180000).toISOString(),
          progress: 0,
          retry_count: 0,
          max_retries: 3,
          metadata: {
            user_id: 'user456',
            estimated_duration: 60
          }
        },
        {
          id: 'job_3',
          type: 'transformation',
          status: 'failed',
          priority: 'high',
          created_at: new Date(Date.now() - 600000).toISOString(),
          started_at: new Date(Date.now() - 480000).toISOString(),
          completed_at: new Date(Date.now() - 420000).toISOString(),
          progress: 100,
          retry_count: 2,
          max_retries: 3,
          error_message: 'Timeout: Operation exceeded maximum allowed time',
          metadata: {
            user_id: 'user789',
            session_id: 'sess_xyz'
          }
        },
        {
          id: 'job_4',
          type: 'export',
          status: 'completed',
          priority: 'low',
          created_at: new Date(Date.now() - 900000).toISOString(),
          started_at: new Date(Date.now() - 840000).toISOString(),
          completed_at: new Date(Date.now() - 780000).toISOString(),
          progress: 100,
          retry_count: 0,
          max_retries: 3,
          metadata: {
            user_id: 'user123'
          }
        }
      ];

      setJobs(mockJobs);

      // Calculate stats
      const newStats: QueueStats = {
        total_jobs: mockJobs.length,
        pending_jobs: mockJobs.filter(j => j.status === 'pending').length,
        running_jobs: mockJobs.filter(j => j.status === 'running').length,
        completed_jobs: mockJobs.filter(j => j.status === 'completed').length,
        failed_jobs: mockJobs.filter(j => j.status === 'failed').length,
        avg_processing_time: 145,
        throughput_per_hour: 24,
        error_rate: 8.5
      };

      setStats(newStats);

      // Fetch actual queue data
      const response = await fetch('/api/queue');
      if (response.ok) {
        const data = await response.json();
        if (data.jobs) {
          setJobs(data.jobs);
        }
        if (data.stats) {
          setStats(data.stats);
        }
      }
    } catch (error) {
      console.error('Failed to fetch queue data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchQueueData();
    const interval = setInterval(fetchQueueData, 5000); // Update every 5 seconds
    return () => clearInterval(interval);
  }, []);

  const handleJobAction = async (jobId: string, action: 'cancel' | 'retry' | 'priority') => {
    try {
      const response = await fetch(`/api/queue/jobs/${jobId}/${action}`, {
        method: 'POST'
      });
      
      if (response.ok) {
        fetchQueueData();
      }
    } catch (error) {
      console.error(`Failed to ${action} job:`, error);
    }
  };

  const handleQueueAction = async (action: 'pause' | 'resume' | 'clear') => {
    try {
      const response = await fetch(`/api/queue/${action}`, {
        method: 'POST'
      });
      
      if (response.ok) {
        if (action === 'pause') setQueuePaused(true);
        if (action === 'resume') setQueuePaused(false);
        fetchQueueData();
      }
    } catch (error) {
      console.error(`Failed to ${action} queue:`, error);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending': return <Clock className="w-4 h-4 text-gray-500" />;
      case 'running': return <Play className="w-4 h-4 text-blue-500" />;
      case 'completed': return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'failed': return <AlertTriangle className="w-4 h-4 text-red-500" />;
      case 'retrying': return <RotateCcw className="w-4 h-4 text-orange-500" />;
      case 'cancelled': return <Square className="w-4 h-4 text-gray-500" />;
      default: return <Activity className="w-4 h-4 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'text-gray-500';
      case 'running': return 'text-blue-500';
      case 'completed': return 'text-green-500';
      case 'failed': return 'text-red-500';
      case 'retrying': return 'text-orange-500';
      case 'cancelled': return 'text-gray-500';
      default: return 'text-gray-500';
    }
  };

  const getJobTypeIcon = (type: string) => {
    const jobType = JOB_TYPES.find(t => t.value === type);
    return jobType?.icon || <Activity className="w-4 h-4" />;
  };

  const filteredJobs = jobs.filter(job => {
    if (selectedStatus !== 'all' && job.status !== selectedStatus) return false;
    if (selectedType !== 'all' && job.type !== selectedType) return false;
    return true;
  });

  return (
    <Card className={cn("w-full", className)}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Queue className="w-6 h-6 text-primary" />
            <div>
              <CardTitle className="text-xl">Queue Management</CardTitle>
              <CardDescription>
                Monitor and manage job queues, priorities, and processing status
              </CardDescription>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            <Button
              variant={queuePaused ? 'default' : 'outline'}
              size="sm"
              onClick={() => handleQueueAction(queuePaused ? 'resume' : 'pause')}
            >
              {queuePaused ? <Play className="w-4 h-4 mr-2" /> : <Pause className="w-4 h-4 mr-2" />}
              {queuePaused ? 'Resume' : 'Pause'}
            </Button>
            <Button variant="outline" size="sm" onClick={() => handleQueueAction('clear')}>
              <Trash2 className="w-4 h-4 mr-2" />
              Clear
            </Button>
            <Button variant="outline" size="sm" onClick={fetchQueueData} disabled={isLoading}>
              <RefreshCw className={cn("w-4 h-4 mr-2", isLoading && "animate-spin")} />
              Refresh
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Queue Status Alert */}
        {queuePaused && (
          <Alert className="border-yellow-500/30 bg-yellow-900/10">
            <Pause className="h-4 w-4 text-yellow-500" />
            <AlertDescription className="text-yellow-200">
              Queue processing is currently paused. New jobs will not be processed until resumed.
            </AlertDescription>
          </Alert>
        )}

        {/* Queue Statistics */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <Card className="p-4">
            <div className="flex items-center space-x-2">
              <Activity className="w-5 h-5 text-blue-500" />
              <div>
                <Typography variant="caption" className="text-muted-foreground">Total Jobs</Typography>
                <Typography variant="h6" className="font-mono">{stats.total_jobs}</Typography>
              </div>
            </div>
          </Card>

          <Card className="p-4">
            <div className="flex items-center space-x-2">
              <Clock className="w-5 h-5 text-gray-500" />
              <div>
                <Typography variant="caption" className="text-muted-foreground">Pending</Typography>
                <Typography variant="h6" className="font-mono">{stats.pending_jobs}</Typography>
              </div>
            </div>
          </Card>

          <Card className="p-4">
            <div className="flex items-center space-x-2">
              <Play className="w-5 h-5 text-blue-500" />
              <div>
                <Typography variant="caption" className="text-muted-foreground">Running</Typography>
                <Typography variant="h6" className="font-mono">{stats.running_jobs}</Typography>
              </div>
            </div>
          </Card>

          <Card className="p-4">
            <div className="flex items-center space-x-2">
              <BarChart3 className="w-5 h-5 text-green-500" />
              <div>
                <Typography variant="caption" className="text-muted-foreground">Throughput/hr</Typography>
                <Typography variant="h6" className="font-mono">{stats.throughput_per_hour}</Typography>
              </div>
            </div>
          </Card>
        </div>

        {/* Filters */}
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="space-y-2">
            <Typography variant="caption" className="text-muted-foreground">Status</Typography>
            <Select value={selectedStatus} onValueChange={setSelectedStatus}>
              <SelectTrigger className="w-40">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
                <SelectItem value="running">Running</SelectItem>
                <SelectItem value="completed">Completed</SelectItem>
                <SelectItem value="failed">Failed</SelectItem>
                <SelectItem value="retrying">Retrying</SelectItem>
                <SelectItem value="cancelled">Cancelled</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Typography variant="caption" className="text-muted-foreground">Type</Typography>
            <Select value={selectedType} onValueChange={setSelectedType}>
              <SelectTrigger className="w-40">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                {JOB_TYPES.map(type => (
                  <SelectItem key={type.value} value={type.value}>
                    {type.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Jobs List */}
        <ScrollArea className="h-96">
          <div className="space-y-3">
            {filteredJobs.length === 0 ? (
              <div className="text-center py-8">
                <Queue className="w-8 h-8 text-muted-foreground mx-auto mb-2" />
                <Typography variant="body-sm" className="text-muted-foreground">
                  No jobs found matching the current filters
                </Typography>
              </div>
            ) : (
              filteredJobs.map((job) => (
                <Card key={job.id} className="p-4">
                  <div className="space-y-3">
                    {/* Job Header */}
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        {getStatusIcon(job.status)}
                        <div>
                          <div className="flex items-center space-x-2">
                            {getJobTypeIcon(job.type)}
                            <Typography variant="body-sm" className="font-medium">
                              {JOB_TYPES.find(t => t.value === job.type)?.label || job.type}
                            </Typography>
                          </div>
                          <Typography variant="caption" className="text-muted-foreground">
                            ID: {job.id}
                          </Typography>
                        </div>
                      </div>
                      
                      <div className="flex items-center space-x-2">
                        <Badge variant="outline" className={cn("text-xs", getStatusColor(job.status))}>
                          {job.status.toUpperCase()}
                        </Badge>
                        <Badge variant="outline" className={cn("text-xs", PRIORITY_COLORS[job.priority])}>
                          {job.priority.toUpperCase()}
                        </Badge>
                      </div>
                    </div>

                    {/* Progress Bar */}
                    {job.status === 'running' && (
                      <div className="space-y-1">
                        <div className="flex justify-between text-xs">
                          <span>Progress</span>
                          <span>{job.progress}%</span>
                        </div>
                        <Progress value={job.progress} className="h-2" />
                      </div>
                    )}

                    {/* Job Details */}
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                      <div>
                        <Typography variant="caption" className="text-muted-foreground">Created</Typography>
                        <Typography variant="body-sm">
                          {formatDistanceToNow(new Date(job.created_at), { addSuffix: true })}
                        </Typography>
                      </div>
                      
                      {job.started_at && (
                        <div>
                          <Typography variant="caption" className="text-muted-foreground">Started</Typography>
                          <Typography variant="body-sm">
                            {formatDistanceToNow(new Date(job.started_at), { addSuffix: true })}
                          </Typography>
                        </div>
                      )}
                      
                      <div>
                        <Typography variant="caption" className="text-muted-foreground">Retries</Typography>
                        <Typography variant="body-sm">
                          {job.retry_count}/{job.max_retries}
                        </Typography>
                      </div>
                      
                      {job.metadata.estimated_duration && (
                        <div>
                          <Typography variant="caption" className="text-muted-foreground">Est. Duration</Typography>
                          <Typography variant="body-sm">
                            {job.metadata.estimated_duration}s
                          </Typography>
                        </div>
                      )}
                    </div>

                    {/* Error Message */}
                    {job.error_message && (
                      <Alert className="border-red-500/30 bg-red-900/10">
                        <AlertTriangle className="h-4 w-4 text-red-500" />
                        <AlertDescription className="text-red-200 text-sm">
                          {job.error_message}
                        </AlertDescription>
                      </Alert>
                    )}

                    {/* Resource Usage */}
                    {job.metadata.resource_usage && (
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <Typography variant="caption" className="text-muted-foreground">CPU Usage</Typography>
                          <Progress value={job.metadata.resource_usage.cpu} className="h-1 mt-1" />
                          <Typography variant="caption">{job.metadata.resource_usage.cpu}%</Typography>
                        </div>
                        <div>
                          <Typography variant="caption" className="text-muted-foreground">Memory</Typography>
                          <Typography variant="body-sm">{job.metadata.resource_usage.memory}MB</Typography>
                        </div>
                      </div>
                    )}

                    {/* Actions */}
                    <div className="flex items-center justify-end space-x-2 pt-2 border-t">
                      {job.status === 'running' && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleJobAction(job.id, 'cancel')}
                        >
                          <Square className="w-4 h-4 mr-1" />
                          Cancel
                        </Button>
                      )}
                      
                      {job.status === 'failed' && job.retry_count < job.max_retries && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleJobAction(job.id, 'retry')}
                        >
                          <RotateCcw className="w-4 h-4 mr-1" />
                          Retry
                        </Button>
                      )}
                      
                      {job.status === 'pending' && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleJobAction(job.id, 'priority')}
                        >
                          <FastForward className="w-4 h-4 mr-1" />
                          Prioritize
                        </Button>
                      )}
                    </div>
                  </div>
                </Card>
              ))
            )}
          </div>
        </ScrollArea>
      </CardContent>
    </Card>
  );
};

export default QueueManagement;
