
import React, { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ResizableHandle, ResizablePanel, ResizablePanelGroup } from "@/components/ui/resizable";
import {
  BackIcon,
  ApplyIcon,
  DownloadIcon,
  ExamplesIcon,
  HelpIcon,
  PullRequestIcon,
  GitHubIcon,
  HistoryIcon,
  SettingsIcon,
  CodeIcon,
  FileIcon,
  ReactorIcon,
  MoreVerticalIcon
} from "@/components/ui/icon";
import { BarChart3, X, ChevronDown, Zap, Monitor, User, Menu } from "lucide-react";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { ThemeToggle } from "@/components/theme-provider";
import { AppBreadcrumb } from "@/components/ui/breadcrumb";
import { KeyboardShortcutsProvider, defaultShortcuts } from "@/components/ui/keyboard-shortcuts";
import { useBreakpoint, MobileHeader, MobileBottomNav, MobileDrawer } from "@/components/ui/mobile-layout";
import { useSwipe } from "@/hooks/useGestures";
import { useLocation } from "react-router-dom";
import { cn } from "@/lib/utils";
import { useNavigate } from "react-router-dom";
import { useToast } from "@/hooks/use-toast";
import { CodeEditor } from "@/components/CodeEditor";
import { DiffViewer } from "@/components/DiffViewer";
import { AgentLog } from "@/components/AgentLog";
import { ControlPanel } from "@/components/ControlPanel";
import { CodeExamples } from "@/components/CodeExamples";
import { HelpPanel } from "@/components/HelpPanel";
import { ConfigurationStatus } from "@/components/ConfigurationStatus";
import { useCodeTransformation } from "@/hooks/useCodeTransformation";
import { useReactorLoop } from "@/hooks/useReactorLoop";
import { useSupabaseReactor } from "@/hooks/useSupabaseReactor";
import { useGitHubAuth } from "@/hooks/useGitHubAuth";
import { useTransformationHistory } from "@/hooks/useTransformationHistory";
import { useKeyboardShortcuts } from "@/hooks/useKeyboardShortcuts";
import { StreamPanel } from "@/components/StreamPanel";
import { reactorApi } from "@/services/reactorApi";
import { telemetry } from "@/lib/telemetry";

const Dashboard = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { isMobile, isTablet, isDesktop } = useBreakpoint();
  const { toast } = useToast();

  // Track page view
  useState(() => {
    telemetry.trackPageView('dashboard');
  });
  const [currentPrompt, setCurrentPrompt] = useState(`// Welcome to Metamorphic Reactor
// Describe what you want to transform or improve in your code

Optimize this fibonacci function for better performance and add memoization.

function fibonacci(n) {
  if (n <= 1) return n;
  return fibonacci(n - 1) + fibonacci(n - 2);
}

console.log(fibonacci(10));`);

  const [showExamples, setShowExamples] = useState(false);
  const [showHelp, setShowHelp] = useState(false);
  const [showMonitoring, setShowMonitoring] = useState(false);
  const [activePanel, setActivePanel] = useState<'stream' | 'logs'>('stream');
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [mobilePanelOpen, setMobilePanelOpen] = useState(false);

  // Legacy transformation hook (keeping for compatibility)
  const {
    isRunning: isLegacyRunning,
    logs,
    diffContent,
    transformedCode,
    runTransformation,
    stopTransformation,
    clearLogs
  } = useCodeTransformation();

  // New reactor loop hook
  const {
    isRunning: isReactorRunning,
    events,
    currentPatch,
    finalResult,
    progress,
    runLoop,
    stopLoop,
    clearEvents
  } = useReactorLoop();

  // Supabase reactor hook
  const {
    isRunning: isSupabaseRunning,
    events: supabaseEvents,
    currentPatch: supabasePatch,
    finalResult: supabaseFinalResult,
    progress: supabaseProgress,
    transformationId,
    runLoop: runSupabaseLoop,
    stopLoop: stopSupabaseLoop,
    clearEvents: clearSupabaseEvents,
    createPR
  } = useSupabaseReactor();

  // GitHub authentication hook
  const {
    isConnected: isGitHubConnected,
    user: githubUser,
    isLoading: isGitHubLoading,
    connectGitHub,
    disconnectGitHub
  } = useGitHubAuth();

  // Determine which system is running
  const isRunning = isLegacyRunning || isReactorRunning || isSupabaseRunning;

  const { addToHistory } = useTransformationHistory();

  const handleRunLoop = async () => {
    try {
      if (!currentPrompt.trim()) {
        toast({
          title: "Empty Prompt",
          description: "Please enter a prompt to run the reactor loop.",
          variant: "destructive",
        });
        return;
      }

      // Track reactor run start
      await telemetry.trackReactorRunStart({
        prompt: currentPrompt,
        max_iterations: 10,
        score_threshold: 0.95,
        model_planner: 'gpt-4-turbo',
        model_critic: 'claude-3-sonnet',
      });

      // Use the Supabase reactor loop for production
      await runSupabaseLoop(currentPrompt, 10);

      toast({
        title: "Reactor Loop Started",
        description: "The dual-agent system is analyzing and transforming your code.",
      });
    } catch (error) {
      console.error('Error running reactor loop:', error);
      await telemetry.trackError(error instanceof Error ? error : new Error('Unknown reactor error'), 'reactor_loop');
      toast({
        title: "Reactor Error",
        description: error instanceof Error ? error.message : "An error occurred during the reactor loop process.",
        variant: "destructive",
      });
    }
  };

  const handleStopLoop = () => {
    stopLoop();
    stopSupabaseLoop();
    stopTransformation();
    toast({
      title: "Loop Stopped",
      description: "The reactor loop has been stopped.",
    });
  };

  const handleApplyChanges = () => {
    const codeToApply = transformedCode || (finalResult?.finalPatch ? 'Applied patch from reactor' : null);

    if (codeToApply) {
      // Save to history before applying
      addToHistory({
        originalCode: currentPrompt,
        transformedCode: codeToApply,
        iterations: finalResult?.iterations || 5,
        finalScore: finalResult?.score || 0.95,
        title: `Reactor ${new Date().toLocaleTimeString()}`
      });

      setCurrentPrompt(codeToApply);

      toast({
        title: "Changes Applied",
        description: "The reactor transformations have been applied.",
      });

      // Clear diff after applying
      setTimeout(() => {
        clearLogs();
        clearEvents();
      }, 1000);
    }
  };

  const handleSelectExample = (code: string) => {
    setCurrentPrompt(code);
    setShowExamples(false);
    toast({
      title: "Example Loaded",
      description: "Code example has been loaded into the editor.",
    });
  };

  const handleDownloadCode = () => {
    const codeToDownload = transformedCode || currentPrompt;
    const patchData = finalResult?.finalPatch ? JSON.stringify(finalResult.finalPatch, null, 2) : null;

    const content = patchData
      ? `// Original Prompt:\n${currentPrompt}\n\n// Generated Patch:\n${patchData}`
      : codeToDownload;

    const blob = new Blob([content], { type: 'text/javascript' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = patchData ? 'reactor-patch.json' : 'optimized-code.js';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    toast({
      title: "Content Downloaded",
      description: "Your reactor output has been saved to your downloads folder.",
    });
  };

  const handleCreatePR = async () => {
    if (!finalResult) {
      toast({
        title: "No Results",
        description: "Complete a reactor loop first to create a PR.",
        variant: "destructive",
      });
      return;
    }

    try {
      // For now, we'll simulate a session ID - in real implementation this would come from the loop result
      const sessionId = `session-${Date.now()}`;
      const prResult = await reactorApi.createPR(sessionId);

      toast({
        title: "PR Created!",
        description: `Draft PR created successfully: ${prResult.title}`,
      });

      // Open PR in new tab
      window.open(prResult.url, '_blank');
    } catch (error) {
      toast({
        title: "PR Creation Failed",
        description: error instanceof Error ? error.message : "Failed to create PR",
        variant: "destructive",
      });
    }
  };

  // Enhanced keyboard shortcuts
  const keyboardShortcuts = React.useMemo(() => [
    ...defaultShortcuts.map(shortcut => ({
      ...shortcut,
      action: () => {
        switch (shortcut.key) {
          case 'Ctrl+Enter':
            handleRunLoop();
            break;
          case 'Ctrl+Shift+S':
            handleStopLoop();
            break;
          case 'Ctrl+Shift+A':
            handleApplyChanges();
            break;
          case 'Ctrl+Shift+C':
            clearLogs();
            clearEvents();
            clearSupabaseEvents();
            break;
          case 'Ctrl+S':
            handleDownloadCode();
            break;
          case 'Ctrl+B':
            setShowExamples(!showExamples);
            break;
          case 'Ctrl+H':
            setShowHelp(!showHelp);
            break;
          case 'Ctrl+Shift+P':
            if (finalResult) handleCreatePR();
            break;
        }
      }
    }))
  ], [handleRunLoop, handleStopLoop, handleApplyChanges, handleDownloadCode, handleCreatePR, showExamples, showHelp, finalResult]);

  // Mobile navigation items
  const mobileNavItems = [
    {
      id: 'editor',
      label: 'Editor',
      icon: <CodeIcon size="sm" />,
    },
    {
      id: 'diff',
      label: 'Diff',
      icon: <FileIcon size="sm" />,
    },
    {
      id: 'console',
      label: 'Console',
      icon: <ReactorIcon size="sm" />,
      badge: isRunning ? '●' : undefined
    },
    {
      id: 'menu',
      label: 'Menu',
      icon: <MoreVerticalIcon size="sm" />,
    }
  ];

  const [activeMobilePanel, setActiveMobilePanel] = useState('editor');

  // Mobile gesture navigation
  const swipeHandlers = useSwipe({
    onSwipeLeft: () => {
      if (isMobile || isTablet) {
        const panels = ['editor', 'diff', 'console'];
        const currentIndex = panels.indexOf(activeMobilePanel);
        const nextIndex = (currentIndex + 1) % panels.length;
        setActiveMobilePanel(panels[nextIndex]);
      }
    },
    onSwipeRight: () => {
      if (isMobile || isTablet) {
        const panels = ['editor', 'diff', 'console'];
        const currentIndex = panels.indexOf(activeMobilePanel);
        const prevIndex = currentIndex === 0 ? panels.length - 1 : currentIndex - 1;
        setActiveMobilePanel(panels[prevIndex]);
      }
    },
    threshold: 50,
    velocityThreshold: 0.3
  });

  return (
    <KeyboardShortcutsProvider shortcuts={keyboardShortcuts}>
      <div className="min-h-screen bg-background">
        {/* Desktop Header */}
        {isDesktop && (
          <header className="border-b border-border glass-strong" role="banner">
            <div className="flex items-center justify-between px-6 py-4">
              <div className="flex items-center space-x-4">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => navigate('/')}
                  className="text-muted-foreground hover:text-foreground transition-colors focus-ring"
                >
                  <BackIcon className="mr-2" aria-hidden="true" />
                  Back
                </Button>
                <h1 className="text-h2 text-foreground">Metamorphic Reactor</h1>
                <div className="flex items-center space-x-2">
                  <Badge variant="secondary" className="bg-primary/20 text-primary border-primary/30">
                    Dashboard
                  </Badge>
                  <div className="flex items-center space-x-1">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span className="text-xs text-muted-foreground">System Online</span>
                  </div>
                </div>
              </div>
          <div className="flex items-center space-x-3">
            {/* Primary Actions - Always Visible */}
            <div className="flex items-center space-x-2">
              {/* Quick Actions for Active Transformation */}
              {(transformedCode || finalResult) && (
                <div className="flex items-center space-x-2 px-3 py-1 bg-success/10 rounded-lg border border-success/20">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleApplyChanges}
                    className="text-success hover:bg-success/20 h-8 px-3"
                    title="Apply Changes (Ctrl+Enter)"
                  >
                    <ApplyIcon className="mr-1 h-4 w-4" aria-hidden="true" />
                    Apply
                  </Button>
                  {finalResult && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={handleCreatePR}
                      className="text-primary hover:bg-primary/20 h-8 px-3"
                      title="Create Pull Request (Ctrl+P)"
                    >
                      <PullRequestIcon className="mr-1 h-4 w-4" aria-hidden="true" />
                      PR
                    </Button>
                  )}
                </div>
              )}

              {/* Learning & Help */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="outline"
                    size="sm"
                    className="border-border text-muted-foreground hover:bg-accent hover:text-accent-foreground transition-colors focus-ring"
                  >
                    <ExamplesIcon className="mr-1 h-4 w-4" aria-hidden="true" />
                    Learn
                    <ChevronDown className="ml-1 h-3 w-3" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-48">
                  <DropdownMenuItem onClick={() => setShowExamples(!showExamples)}>
                    <ExamplesIcon className="mr-2 h-4 w-4" />
                    Code Examples
                    <span className="ml-auto text-xs text-muted-foreground">E</span>
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => setShowHelp(!showHelp)}>
                    <HelpIcon className="mr-2 h-4 w-4" />
                    Help & Docs
                    <span className="ml-auto text-xs text-muted-foreground">?</span>
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={() => navigate('/history')}>
                    <HistoryIcon className="mr-2 h-4 w-4" />
                    History
                    <span className="ml-auto text-xs text-muted-foreground">H</span>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>

              {/* Monitoring & Analytics */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="outline"
                    size="sm"
                    className={cn(
                      "border-border text-muted-foreground hover:bg-accent hover:text-accent-foreground transition-colors focus-ring",
                      showMonitoring && "bg-accent text-accent-foreground"
                    )}
                  >
                    <Monitor className="mr-1 h-4 w-4" aria-hidden="true" />
                    Monitor
                    <ChevronDown className="ml-1 h-3 w-3" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-52">
                  <DropdownMenuItem onClick={() => setShowMonitoring(!showMonitoring)}>
                    <BarChart3 className="mr-2 h-4 w-4" />
                    {showMonitoring ? 'Hide' : 'Show'} Quick Monitor
                    <span className="ml-auto text-xs text-muted-foreground">M</span>
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => navigate('/monitoring')}>
                    <Monitor className="mr-2 h-4 w-4" />
                    Full Dashboard
                    <span className="ml-auto text-xs text-muted-foreground">Ctrl+M</span>
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem>
                    <Zap className="mr-2 h-4 w-4" />
                    Performance
                  </DropdownMenuItem>
                  <DropdownMenuItem>
                    <BarChart3 className="mr-2 h-4 w-4" />
                    Analytics
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>

              {/* File Operations */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="outline"
                    size="sm"
                    className="border-border text-muted-foreground hover:bg-accent hover:text-accent-foreground transition-colors focus-ring"
                  >
                    <FileIcon className="mr-1 h-4 w-4" aria-hidden="true" />
                    File
                    <ChevronDown className="ml-1 h-3 w-3" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-48">
                  <DropdownMenuItem onClick={handleDownloadCode}>
                    <DownloadIcon className="mr-2 h-4 w-4" />
                    Download Code
                    <span className="ml-auto text-xs text-muted-foreground">Ctrl+S</span>
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={() => navigate('/settings')}>
                    <SettingsIcon className="mr-2 h-4 w-4" />
                    Settings
                    <span className="ml-auto text-xs text-muted-foreground">Ctrl+,</span>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>

            {/* Right Side Actions */}
            <div className="flex items-center space-x-2 border-l border-border pl-3">
              <ThemeToggle />
            </div>
          </div>
            </div>

            {/* Breadcrumb Navigation */}
            <div className="px-6 py-2 border-t border-border/50">
              <AppBreadcrumb pathname={location.pathname} />
            </div>
          </header>
        )}

        {/* Mobile Header */}
        {(isMobile || isTablet) && (
          <MobileHeader
            title="Metamorphic Reactor"
            subtitle="Dashboard"
            onBack={() => navigate('/')}
            actions={
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setMobileMenuOpen(true)}
                className="h-8 w-8 p-0"
              >
                <MoreVerticalIcon />
              </Button>
            }
          />
        )}

      {/* Configuration Status */}
      <div className="px-6 py-2">
        <ConfigurationStatus />
      </div>

      {/* Main Content */}
      <main className={cn(
        "role-main",
        isDesktop ? "h-[calc(100vh-73px)]" : "h-[calc(100vh-56px)] pb-16"
      )} role="main">
        {isDesktop ? (
          <ResizablePanelGroup direction="horizontal">
          {/* Left Panel - Prompt Editor */}
          <ResizablePanel defaultSize={40} minSize={30}>
            <div className="h-full flex flex-col">
              <div className="flex items-center justify-between p-4 border-b border-border bg-card">
                <h2 className="text-h5 text-foreground">Reactor Prompt</h2>
                <ControlPanel
                  isRunning={isRunning}
                  onRunLoop={handleRunLoop}
                  onStop={handleStopLoop}
                />
              </div>
              <div className="flex-1">
                <CodeEditor
                  value={currentPrompt}
                  onChange={setCurrentPrompt}
                  language="javascript"
                  ariaLabel="Code input editor"
                  ariaDescribedBy="prompt-editor-description"
                />
              </div>
            </div>
          </ResizablePanel>

          <ResizableHandle className="w-1 bg-border hover:bg-border/80 transition-colors" />

          {/* Center Panel - Diff Viewer */}
          <ResizablePanel defaultSize={35} minSize={25}>
            <div className="h-full flex flex-col">
              <div className="flex items-center justify-between p-4 border-b border-border bg-card">
                <h2 className="text-h5 text-foreground">Patch Viewer</h2>
                <div className="flex items-center space-x-2">
                  {(diffContent || currentPatch || supabasePatch) && (
                    <Badge className="bg-primary/20 text-primary border-primary/30">
                      {(currentPatch || supabasePatch) ? 'JSON Patch' : 'Diff'} Ready
                    </Badge>
                  )}
                  {!isGitHubLoading && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={isGitHubConnected ? disconnectGitHub : connectGitHub}
                      className={`text-xs transition-colors focus-ring ${isGitHubConnected
                        ? 'text-success hover:text-success/80'
                        : 'text-muted-foreground hover:text-foreground'
                      }`}
                    >
                      <GitHubIcon size="xs" className="mr-1" aria-hidden="true" />
                      {isGitHubConnected ? 'Connected' : 'Connect GitHub'}
                    </Button>
                  )}
                </div>
              </div>
              <div className="flex-1">
                <DiffViewer
                  diffContent={diffContent}
                  patch={currentPatch || supabasePatch}
                  onCreatePR={isGitHubConnected && transformationId ? createPR : undefined}
                  onDownload={handleDownloadCode}
                />
              </div>
            </div>
          </ResizablePanel>

          <ResizableHandle className="w-1 bg-border hover:bg-border/80 transition-colors" />

          {/* Right Panel - Stream & Logs */}
          <ResizablePanel defaultSize={showMonitoring ? 20 : 25} minSize={15}>
            <div className="h-full flex flex-col">
              <div className="flex items-center justify-between p-4 border-b border-border bg-card">
                <div className="flex items-center space-x-2">
                  <h2 className="text-h5 text-foreground">
                    {showExamples ? 'Code Examples' : showHelp ? 'Help' : 'Reactor Console'}
                  </h2>
                  {!showExamples && !showHelp && (
                    <div className="flex items-center space-x-1" role="tablist">
                      <Button
                        variant={activePanel === 'stream' ? 'default' : 'ghost'}
                        size="sm"
                        onClick={() => setActivePanel('stream')}
                        className="text-xs h-6 px-2 focus-ring"
                        role="tab"
                        aria-selected={activePanel === 'stream'}
                      >
                        Stream
                      </Button>
                      <Button
                        variant={activePanel === 'logs' ? 'default' : 'ghost'}
                        size="sm"
                        onClick={() => setActivePanel('logs')}
                        className="text-xs h-6 px-2 focus-ring"
                        role="tab"
                        aria-selected={activePanel === 'logs'}
                      >
                        Logs
                      </Button>
                    </div>
                  )}
                </div>
                {isRunning && !showExamples && !showHelp && (
                  <div className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-status-running rounded-full animate-pulse"></div>
                    <span className="text-body-sm text-status-running">Running</span>
                  </div>
                )}
              </div>
              <div className="flex-1 overflow-hidden">
                {showExamples ? (
                  <div className="h-full overflow-y-auto">
                    <CodeExamples onSelectExample={handleSelectExample} />
                  </div>
                ) : showHelp ? (
                  <div className="p-4">
                    <HelpPanel />
                  </div>
                ) : activePanel === 'stream' ? (
                  <StreamPanel
                    isStreaming={isSupabaseRunning}
                    onStart={handleRunLoop}
                    onStop={handleStopLoop}
                    onClear={clearSupabaseEvents}
                    events={supabaseEvents}
                  />
                ) : (
                  <AgentLog logs={logs} />
                )}
              </div>
            </div>
          </ResizablePanel>

          {/* Monitoring Panel - Conditional */}
          {showMonitoring && (
            <>
              <ResizableHandle className="w-1 bg-border hover:bg-border/80 transition-colors" />
              <ResizablePanel defaultSize={20} minSize={15} maxSize={30}>
                <div className="h-full flex flex-col">
                  <div className="flex items-center justify-between p-4 border-b border-border bg-card">
                    <h2 className="text-h5 text-foreground">Quick Monitor</h2>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setShowMonitoring(false)}
                      className="h-6 w-6 p-0"
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                  <div className="flex-1 overflow-y-auto p-4 space-y-4">
                    {/* System Status */}
                    <div className="space-y-2">
                      <h3 className="text-sm font-medium">System Status</h3>
                      <div className="flex items-center space-x-2">
                        <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                        <span className="text-xs text-muted-foreground">All Systems Operational</span>
                      </div>
                    </div>

                    {/* Quick Stats */}
                    <div className="space-y-2">
                      <h3 className="text-sm font-medium">Today's Usage</h3>
                      <div className="space-y-1">
                        <div className="flex justify-between text-xs">
                          <span>Transformations</span>
                          <span>0</span>
                        </div>
                        <div className="flex justify-between text-xs">
                          <span>Cost</span>
                          <span>$0.00</span>
                        </div>
                        <div className="flex justify-between text-xs">
                          <span>Success Rate</span>
                          <span>--</span>
                        </div>
                      </div>
                    </div>

                    {/* Quick Actions */}
                    <div className="space-y-2">
                      <h3 className="text-sm font-medium">Quick Actions</h3>
                      <div className="space-y-1">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => navigate('/monitoring')}
                          className="w-full justify-start text-xs h-8"
                        >
                          <BarChart3 className="w-3 h-3 mr-2" />
                          Full Dashboard
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => navigate('/settings')}
                          className="w-full justify-start text-xs h-8"
                        >
                          <SettingsIcon className="w-3 h-3 mr-2" />
                          Settings
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              </ResizablePanel>
            </>
          )}
          </ResizablePanelGroup>
        ) : (
          /* Mobile Layout */
          <div
            className="h-full flex flex-col"
            onTouchStart={swipeHandlers.onTouchStart}
            onTouchMove={swipeHandlers.onTouchMove}
            onTouchEnd={swipeHandlers.onTouchEnd}
          >
            {activeMobilePanel === 'editor' && (
              <div className="h-full flex flex-col">
                <div className="flex items-center justify-between p-4 border-b border-border bg-card">
                  <h2 className="text-h5 text-foreground">Reactor Prompt</h2>
                  <ControlPanel
                    isRunning={isRunning}
                    onRunLoop={handleRunLoop}
                    onStop={handleStopLoop}
                  />
                </div>
                <div className="flex-1">
                  <CodeEditor
                    value={currentPrompt}
                    onChange={setCurrentPrompt}
                    language="javascript"
                    ariaLabel="Code input editor"
                    height="100%"
                  />
                </div>
              </div>
            )}

            {activeMobilePanel === 'diff' && (
              <div className="h-full flex flex-col">
                <div className="flex items-center justify-between p-4 border-b border-border bg-card">
                  <h2 className="text-h5 text-foreground">Patch Viewer</h2>
                  <div className="flex items-center space-x-2">
                    {(diffContent || currentPatch || supabasePatch) && (
                      <Badge className="bg-primary/20 text-primary border-primary/30">
                        {(currentPatch || supabasePatch) ? 'JSON Patch' : 'Diff'} Ready
                      </Badge>
                    )}
                  </div>
                </div>
                <div className="flex-1">
                  <DiffViewer
                    diffContent={diffContent}
                    patch={currentPatch || supabasePatch}
                    onCreatePR={isGitHubConnected && transformationId ? createPR : undefined}
                    onDownload={handleDownloadCode}
                  />
                </div>
              </div>
            )}

            {activeMobilePanel === 'console' && (
              <div className="h-full flex flex-col">
                <div className="flex items-center justify-between p-4 border-b border-border bg-card">
                  <div className="flex items-center space-x-2">
                    <h2 className="text-h5 text-foreground">
                      {showExamples ? 'Code Examples' : showHelp ? 'Help' : 'Reactor Console'}
                    </h2>
                    {!showExamples && !showHelp && (
                      <div className="flex items-center space-x-1" role="tablist">
                        <Button
                          variant={activePanel === 'stream' ? 'default' : 'ghost'}
                          size="sm"
                          onClick={() => setActivePanel('stream')}
                          className="text-xs h-6 px-2 focus-ring"
                        >
                          Stream
                        </Button>
                        <Button
                          variant={activePanel === 'logs' ? 'default' : 'ghost'}
                          size="sm"
                          onClick={() => setActivePanel('logs')}
                          className="text-xs h-6 px-2 focus-ring"
                        >
                          Logs
                        </Button>
                      </div>
                    )}
                  </div>
                  {isRunning && !showExamples && !showHelp && (
                    <div className="flex items-center space-x-2">
                      <div className="w-2 h-2 bg-status-running rounded-full animate-pulse"></div>
                      <span className="text-body-sm text-status-running">Running</span>
                    </div>
                  )}
                </div>
                <div className="flex-1 overflow-hidden">
                  {showExamples ? (
                    <div className="h-full overflow-y-auto">
                      <CodeExamples onSelectExample={handleSelectExample} />
                    </div>
                  ) : showHelp ? (
                    <div className="p-4">
                      <HelpPanel />
                    </div>
                  ) : activePanel === 'stream' ? (
                    <StreamPanel
                      isStreaming={isSupabaseRunning}
                      onStart={handleRunLoop}
                      onStop={handleStopLoop}
                      onClear={clearSupabaseEvents}
                      events={supabaseEvents}
                    />
                  ) : (
                    <AgentLog logs={logs} />
                  )}
                </div>
              </div>
            )}
          </div>
        )}

        {/* Mobile Bottom Navigation */}
        {(isMobile || isTablet) && (
          <MobileBottomNav
            items={mobileNavItems}
            activeId={activeMobilePanel}
            onItemClick={(id) => {
              if (id === 'menu') {
                setMobileMenuOpen(true);
              } else {
                setActiveMobilePanel(id);
              }
            }}
          />
        )}

        {/* Mobile Menu Drawer */}
        <MobileDrawer
          isOpen={mobileMenuOpen}
          onClose={() => setMobileMenuOpen(false)}
          title="Dashboard Menu"
          position="bottom"
        >
          <div className="p-4 space-y-6">
            {/* Active Transformation Actions */}
            {(transformedCode || finalResult) && (
              <div className="space-y-2">
                <h3 className="text-sm font-medium text-muted-foreground">Active Transformation</h3>
                <div className="space-y-2">
                  <Button
                    variant="outline"
                    onClick={() => {
                      handleApplyChanges();
                      setMobileMenuOpen(false);
                    }}
                    className="w-full justify-start border-success text-success"
                  >
                    <ApplyIcon className="mr-2" />
                    Apply Changes
                  </Button>
                  {finalResult && (
                    <Button
                      variant="outline"
                      onClick={() => {
                        handleCreatePR();
                        setMobileMenuOpen(false);
                      }}
                      className="w-full justify-start border-primary text-primary"
                    >
                      <PullRequestIcon className="mr-2" />
                      Create Pull Request
                    </Button>
                  )}
                </div>
              </div>
            )}

            {/* Learning & Help */}
            <div className="space-y-2">
              <h3 className="text-sm font-medium text-muted-foreground">Learn & Help</h3>
              <div className="space-y-2">
                <Button
                  variant="outline"
                  onClick={() => {
                    setShowExamples(!showExamples);
                    setMobileMenuOpen(false);
                  }}
                  className="w-full justify-start"
                >
                  <ExamplesIcon className="mr-2" />
                  Code Examples
                </Button>
                <Button
                  variant="outline"
                  onClick={() => {
                    setShowHelp(!showHelp);
                    setMobileMenuOpen(false);
                  }}
                  className="w-full justify-start"
                >
                  <HelpIcon className="mr-2" />
                  Help & Documentation
                </Button>
                <Button
                  variant="outline"
                  onClick={() => {
                    navigate('/history');
                    setMobileMenuOpen(false);
                  }}
                  className="w-full justify-start"
                >
                  <HistoryIcon className="mr-2" />
                  History
                </Button>
              </div>
            </div>

            {/* Monitoring & Analytics */}
            <div className="space-y-2">
              <h3 className="text-sm font-medium text-muted-foreground">Monitoring</h3>
              <div className="space-y-2">
                <Button
                  variant="outline"
                  onClick={() => {
                    navigate('/monitoring');
                    setMobileMenuOpen(false);
                  }}
                  className="w-full justify-start"
                >
                  <Monitor className="mr-2" />
                  Full Dashboard
                </Button>
                <Button
                  variant="outline"
                  onClick={() => {
                    setShowMonitoring(!showMonitoring);
                    setMobileMenuOpen(false);
                  }}
                  className="w-full justify-start"
                >
                  <BarChart3 className="mr-2" />
                  {showMonitoring ? 'Hide' : 'Show'} Quick Monitor
                </Button>
              </div>
            </div>

            {/* File Operations */}
            <div className="space-y-2">
              <h3 className="text-sm font-medium text-muted-foreground">File Operations</h3>
              <div className="space-y-2">
                <Button
                  variant="outline"
                  onClick={() => {
                    handleDownloadCode();
                    setMobileMenuOpen(false);
                  }}
                  className="w-full justify-start"
                >
                  <DownloadIcon className="mr-2" />
                  Download Code
                </Button>
                <Button
                  variant="outline"
                  onClick={() => {
                    navigate('/settings');
                    setMobileMenuOpen(false);
                  }}
                  className="w-full justify-start"
                >
                  <SettingsIcon className="mr-2" />
                  Settings
                </Button>
              </div>
            </div>

            {/* Theme Toggle */}
            <div className="pt-4 border-t border-border">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-muted-foreground">Theme</span>
                <ThemeToggle />
              </div>
            </div>
          </div>
        </MobileDrawer>
      </main>
      </div>
    </KeyboardShortcutsProvider>
  );
};

export default Dashboard;
