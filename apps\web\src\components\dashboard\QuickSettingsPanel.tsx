import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Slider } from '@/components/ui/slider';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Settings, 
  Zap, 
  Brain,
  Target,
  Clock,
  DollarSign,
  Save,
  RotateCcw,
  AlertTriangle,
  CheckCircle,
  Cpu,
  Activity
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { HStack, VStack } from '@/components/ui/layout';
import { Typography } from '@/components/ui/typography';

interface ModelConfig {
  planner: string;
  critic: string;
  temperature: number;
  maxTokens: number;
  maxIterations: number;
  scoreThreshold: number;
  costLimit: number;
  timeoutSeconds: number;
}

interface QuickSettingsPanelProps {
  className?: string;
  onSettingsChange?: (settings: ModelConfig) => void;
  initialSettings?: Partial<ModelConfig>;
}

const DEFAULT_SETTINGS: ModelConfig = {
  planner: 'gpt-4-turbo',
  critic: 'claude-3-sonnet',
  temperature: 0.7,
  maxTokens: 4000,
  maxIterations: 10,
  scoreThreshold: 0.95,
  costLimit: 3.0,
  timeoutSeconds: 180
};

const MODEL_OPTIONS = {
  planner: [
    { value: 'gpt-4-turbo', label: 'GPT-4 Turbo', cost: 0.01 },
    { value: 'gpt-4', label: 'GPT-4', cost: 0.03 },
    { value: 'gpt-3.5-turbo', label: 'GPT-3.5 Turbo', cost: 0.002 },
    { value: 'claude-3-opus', label: 'Claude 3 Opus', cost: 0.015 },
    { value: 'claude-3-sonnet', label: 'Claude 3 Sonnet', cost: 0.003 }
  ],
  critic: [
    { value: 'claude-3-sonnet', label: 'Claude 3 Sonnet', cost: 0.003 },
    { value: 'claude-3-opus', label: 'Claude 3 Opus', cost: 0.015 },
    { value: 'gpt-4-turbo', label: 'GPT-4 Turbo', cost: 0.01 },
    { value: 'gpt-4', label: 'GPT-4', cost: 0.03 }
  ]
};

export const QuickSettingsPanel: React.FC<QuickSettingsPanelProps> = ({
  className,
  onSettingsChange,
  initialSettings = {}
}) => {
  const [settings, setSettings] = useState<ModelConfig>({
    ...DEFAULT_SETTINGS,
    ...initialSettings
  });
  
  const [hasChanges, setHasChanges] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [saveStatus, setSaveStatus] = useState<'idle' | 'success' | 'error'>('idle');

  // Track changes
  useEffect(() => {
    const hasChanged = Object.keys(settings).some(key => {
      const currentValue = settings[key as keyof ModelConfig];
      const defaultValue = { ...DEFAULT_SETTINGS, ...initialSettings }[key as keyof ModelConfig];
      return currentValue !== defaultValue;
    });
    setHasChanges(hasChanged);
  }, [settings, initialSettings]);

  const handleSettingChange = <K extends keyof ModelConfig>(
    key: K,
    value: ModelConfig[K]
  ) => {
    setSettings(prev => ({ ...prev, [key]: value }));
    setSaveStatus('idle');
  };

  const handleSave = async () => {
    setIsSaving(true);
    setSaveStatus('idle');
    
    try {
      // Save to localStorage
      localStorage.setItem('reactor-quick-settings', JSON.stringify(settings));
      
      // Call parent callback
      onSettingsChange?.(settings);
      
      setSaveStatus('success');
      setHasChanges(false);
      
      // Clear success status after 2 seconds
      setTimeout(() => setSaveStatus('idle'), 2000);
    } catch (error) {
      console.error('Failed to save settings:', error);
      setSaveStatus('error');
    } finally {
      setIsSaving(false);
    }
  };

  const handleReset = () => {
    setSettings({ ...DEFAULT_SETTINGS, ...initialSettings });
    setSaveStatus('idle');
  };

  const getEstimatedCost = (): number => {
    const plannerModel = MODEL_OPTIONS.planner.find(m => m.value === settings.planner);
    const criticModel = MODEL_OPTIONS.critic.find(m => m.value === settings.critic);
    
    const plannerCost = (plannerModel?.cost || 0.01) * (settings.maxTokens / 1000) * settings.maxIterations;
    const criticCost = (criticModel?.cost || 0.003) * (settings.maxTokens / 1000) * settings.maxIterations;
    
    return plannerCost + criticCost;
  };

  const isNearCostLimit = getEstimatedCost() > settings.costLimit * 0.8;

  return (
    <Card className={cn("w-full", className)}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Settings className="w-5 h-5 text-primary" />
            <CardTitle className="text-lg">Quick Settings</CardTitle>
          </div>
          <div className="flex items-center space-x-2">
            {saveStatus === 'success' && <CheckCircle className="w-4 h-4 text-green-500" />}
            {saveStatus === 'error' && <AlertTriangle className="w-4 h-4 text-red-500" />}
            {hasChanges && <div className="w-2 h-2 bg-yellow-500 rounded-full" />}
          </div>
        </div>
        <CardDescription>
          Quickly adjust model parameters and transformation settings
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Model Selection */}
        <div className="space-y-4">
          <Typography variant="body-sm" className="font-medium">
            Model Configuration
          </Typography>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Planner Model */}
            <div className="space-y-2">
              <Label className="flex items-center space-x-2">
                <Brain className="w-4 h-4 text-blue-500" />
                <span>Planner Model</span>
              </Label>
              <select
                value={settings.planner}
                onChange={(e) => handleSettingChange('planner', e.target.value)}
                className="w-full px-3 py-2 text-sm border border-border rounded-md bg-background"
              >
                {MODEL_OPTIONS.planner.map(model => (
                  <option key={model.value} value={model.value}>
                    {model.label} (${model.cost}/1K tokens)
                  </option>
                ))}
              </select>
            </div>

            {/* Critic Model */}
            <div className="space-y-2">
              <Label className="flex items-center space-x-2">
                <Target className="w-4 h-4 text-purple-500" />
                <span>Critic Model</span>
              </Label>
              <select
                value={settings.critic}
                onChange={(e) => handleSettingChange('critic', e.target.value)}
                className="w-full px-3 py-2 text-sm border border-border rounded-md bg-background"
              >
                {MODEL_OPTIONS.critic.map(model => (
                  <option key={model.value} value={model.value}>
                    {model.label} (${model.cost}/1K tokens)
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>

        {/* Parameter Controls */}
        <div className="space-y-4">
          <Typography variant="body-sm" className="font-medium">
            Generation Parameters
          </Typography>
          
          {/* Temperature */}
          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <Label className="flex items-center space-x-2">
                <Zap className="w-4 h-4 text-orange-500" />
                <span>Temperature</span>
              </Label>
              <Badge variant="outline" className="font-mono text-xs">
                {settings.temperature.toFixed(2)}
              </Badge>
            </div>
            <Slider
              value={[settings.temperature]}
              onValueChange={([value]) => handleSettingChange('temperature', value)}
              min={0}
              max={2}
              step={0.1}
              className="w-full"
            />
            <div className="flex justify-between text-xs text-muted-foreground">
              <span>Conservative</span>
              <span>Creative</span>
            </div>
          </div>

          {/* Max Tokens */}
          <div className="space-y-2">
            <Label className="flex items-center space-x-2">
              <Cpu className="w-4 h-4 text-green-500" />
              <span>Max Tokens</span>
            </Label>
            <Input
              type="number"
              value={settings.maxTokens}
              onChange={(e) => handleSettingChange('maxTokens', parseInt(e.target.value) || 4000)}
              min={1000}
              max={8000}
              step={500}
              className="font-mono"
            />
          </div>
        </div>

        {/* Loop Configuration */}
        <div className="space-y-4">
          <Typography variant="body-sm" className="font-medium">
            Loop Configuration
          </Typography>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Max Iterations */}
            <div className="space-y-2">
              <Label className="flex items-center space-x-2">
                <Activity className="w-4 h-4 text-blue-500" />
                <span>Max Iterations</span>
              </Label>
              <Input
                type="number"
                value={settings.maxIterations}
                onChange={(e) => handleSettingChange('maxIterations', parseInt(e.target.value) || 10)}
                min={1}
                max={20}
                className="font-mono"
              />
            </div>

            {/* Score Threshold */}
            <div className="space-y-2">
              <Label className="flex items-center space-x-2">
                <Target className="w-4 h-4 text-purple-500" />
                <span>Score Threshold</span>
              </Label>
              <Input
                type="number"
                value={settings.scoreThreshold}
                onChange={(e) => handleSettingChange('scoreThreshold', parseFloat(e.target.value) || 0.95)}
                min={0.5}
                max={1.0}
                step={0.05}
                className="font-mono"
              />
            </div>
          </div>
        </div>

        {/* Safety Limits */}
        <div className="space-y-4">
          <Typography variant="body-sm" className="font-medium">
            Safety Limits
          </Typography>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Cost Limit */}
            <div className="space-y-2">
              <Label className="flex items-center space-x-2">
                <DollarSign className="w-4 h-4 text-green-500" />
                <span>Cost Limit ($)</span>
              </Label>
              <Input
                type="number"
                value={settings.costLimit}
                onChange={(e) => handleSettingChange('costLimit', parseFloat(e.target.value) || 3.0)}
                min={0.5}
                max={10.0}
                step={0.5}
                className="font-mono"
              />
            </div>

            {/* Timeout */}
            <div className="space-y-2">
              <Label className="flex items-center space-x-2">
                <Clock className="w-4 h-4 text-orange-500" />
                <span>Timeout (seconds)</span>
              </Label>
              <Input
                type="number"
                value={settings.timeoutSeconds}
                onChange={(e) => handleSettingChange('timeoutSeconds', parseInt(e.target.value) || 180)}
                min={60}
                max={600}
                step={30}
                className="font-mono"
              />
            </div>
          </div>
        </div>

        {/* Cost Estimation */}
        <div className="p-3 rounded-lg border border-border bg-card/50">
          <div className="flex justify-between items-center mb-2">
            <Typography variant="body-sm" className="font-medium">
              Estimated Cost per Loop
            </Typography>
            <Badge variant={isNearCostLimit ? 'destructive' : 'secondary'}>
              ${getEstimatedCost().toFixed(4)}
            </Badge>
          </div>
          <Typography variant="caption" className="text-muted-foreground">
            Based on current model selection and parameters
          </Typography>
          {isNearCostLimit && (
            <Alert className="mt-2 border-yellow-500/30 bg-yellow-900/10">
              <AlertTriangle className="h-4 w-4 text-yellow-500" />
              <AlertDescription className="text-yellow-200 text-xs">
                Estimated cost is near your limit of ${settings.costLimit}
              </AlertDescription>
            </Alert>
          )}
        </div>

        {/* Action Buttons */}
        <div className="flex justify-between items-center pt-4 border-t border-border">
          <Button
            variant="outline"
            size="sm"
            onClick={handleReset}
            disabled={!hasChanges}
          >
            <RotateCcw className="w-4 h-4 mr-2" />
            Reset
          </Button>
          
          <Button
            onClick={handleSave}
            disabled={!hasChanges || isSaving}
            size="sm"
          >
            <Save className="w-4 h-4 mr-2" />
            {isSaving ? 'Saving...' : 'Save Settings'}
          </Button>
        </div>

        {/* Save Status */}
        {saveStatus === 'success' && (
          <Alert className="border-green-500/30 bg-green-900/10">
            <CheckCircle className="h-4 w-4 text-green-500" />
            <AlertDescription className="text-green-200">
              Settings saved successfully
            </AlertDescription>
          </Alert>
        )}
        
        {saveStatus === 'error' && (
          <Alert className="border-red-500/30 bg-red-900/10">
            <AlertTriangle className="h-4 w-4 text-red-500" />
            <AlertDescription className="text-red-200">
              Failed to save settings. Please try again.
            </AlertDescription>
          </Alert>
        )}
      </CardContent>
    </Card>
  );
};

export default QuickSettingsPanel;
