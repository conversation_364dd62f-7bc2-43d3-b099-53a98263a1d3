import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Activity, 
  BarChart3, 
  Settings, 
  Bell,
  Shield,
  Users,
  Zap,
  Monitor
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useBreakpoint } from '@/components/ui/mobile-layout';

// Import all the dashboard widgets
import PerformanceMetricsWidget from './PerformanceMetricsWidget';
import CostTrackingWidget from './CostTrackingWidget';
import UsageStatisticsPanel from './UsageStatisticsPanel';
import SystemHealthIndicators from './SystemHealthIndicators';
import RecentActivityTimeline from './RecentActivityTimeline';
import QuickSettingsPanel from './QuickSettingsPanel';
import NotificationCenter from './NotificationCenter';
import { MobileDashboardLayout } from './MobileEnhancements';
import DashboardErrorBoundary from './DashboardErrorBoundary';

// Import admin components
import AdminPanel from '../admin/AdminPanel';
import ApiKeyManagement from '../admin/ApiKeyManagement';
import LogsViewer from '../admin/LogsViewer';
import QueueManagement from '../admin/QueueManagement';

// Import other components
import OnboardingWizard from '../onboarding/OnboardingWizard';
import WorkspaceManager from '../workspace/WorkspaceManager';

interface DashboardIntegrationProps {
  className?: string;
  showOnboarding?: boolean;
  onOnboardingComplete?: () => void;
}

export const DashboardIntegration: React.FC<DashboardIntegrationProps> = ({
  className,
  showOnboarding = false,
  onOnboardingComplete
}) => {
  const { isMobile, isTablet } = useBreakpoint();
  const [activeTab, setActiveTab] = useState('overview');
  const [modelConfig, setModelConfig] = useState({
    planner: 'gpt-4-turbo',
    critic: 'claude-3-sonnet',
    temperature: 0.7,
    maxTokens: 4000,
    maxIterations: 10,
    scoreThreshold: 0.95,
    costLimit: 3.0,
    timeoutSeconds: 180
  });

  // Show onboarding wizard if needed
  if (showOnboarding) {
    return (
      <div className={cn("min-h-screen bg-background p-4", className)}>
        <OnboardingWizard onComplete={onOnboardingComplete || (() => {})} />
      </div>
    );
  }

  // Desktop layout with tabs
  const DesktopDashboard = () => (
    <div className={cn("w-full space-y-6", className)}>
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Dashboard</h1>
          <p className="text-muted-foreground">
            Monitor your code transformations, system health, and usage analytics
          </p>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview" className="flex items-center space-x-2">
            <Monitor className="w-4 h-4" />
            <span>Overview</span>
          </TabsTrigger>
          <TabsTrigger value="analytics" className="flex items-center space-x-2">
            <BarChart3 className="w-4 h-4" />
            <span>Analytics</span>
          </TabsTrigger>
          <TabsTrigger value="admin" className="flex items-center space-x-2">
            <Shield className="w-4 h-4" />
            <span>Admin</span>
          </TabsTrigger>
          <TabsTrigger value="workspace" className="flex items-center space-x-2">
            <Settings className="w-4 h-4" />
            <span>Workspace</span>
          </TabsTrigger>
          <TabsTrigger value="notifications" className="flex items-center space-x-2">
            <Bell className="w-4 h-4" />
            <span>Alerts</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
            <DashboardErrorBoundary name="PerformanceMetrics" level="widget">
              <PerformanceMetricsWidget />
            </DashboardErrorBoundary>
            
            <DashboardErrorBoundary name="CostTracking" level="widget">
              <CostTrackingWidget />
            </DashboardErrorBoundary>
            
            <DashboardErrorBoundary name="SystemHealth" level="widget">
              <SystemHealthIndicators />
            </DashboardErrorBoundary>
            
            <DashboardErrorBoundary name="QuickSettings" level="widget">
              <QuickSettingsPanel 
                onSettingsChange={setModelConfig}
                initialSettings={modelConfig}
              />
            </DashboardErrorBoundary>
            
            <div className="lg:col-span-2">
              <DashboardErrorBoundary name="RecentActivity" level="widget">
                <RecentActivityTimeline />
              </DashboardErrorBoundary>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <DashboardErrorBoundary name="UsageStatistics" level="widget">
              <UsageStatisticsPanel />
            </DashboardErrorBoundary>
            
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <BarChart3 className="w-5 h-5" />
                    <span>Advanced Analytics</span>
                  </CardTitle>
                  <CardDescription>
                    Detailed performance insights and trends
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground">
                    Advanced analytics dashboard with charts, trends, and custom reporting.
                    This would include transformation success rates, cost optimization insights,
                    and performance benchmarks.
                  </p>
                </CardContent>
              </Card>
              
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Users className="w-5 h-5" />
                    <span>Collaboration Metrics</span>
                  </CardTitle>
                  <CardDescription>
                    Team usage and collaboration insights
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground">
                    Team collaboration metrics including shared transformations,
                    comment activity, and knowledge sharing patterns.
                  </p>
                </CardContent>
              </Card>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="admin" className="space-y-6">
          <Tabs defaultValue="overview" className="w-full">
            <TabsList>
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="api-keys">API Keys</TabsTrigger>
              <TabsTrigger value="logs">Logs</TabsTrigger>
              <TabsTrigger value="queue">Queue</TabsTrigger>
            </TabsList>
            
            <TabsContent value="overview">
              <DashboardErrorBoundary name="AdminPanel" level="component">
                <AdminPanel />
              </DashboardErrorBoundary>
            </TabsContent>
            
            <TabsContent value="api-keys">
              <DashboardErrorBoundary name="ApiKeyManagement" level="component">
                <ApiKeyManagement />
              </DashboardErrorBoundary>
            </TabsContent>
            
            <TabsContent value="logs">
              <DashboardErrorBoundary name="LogsViewer" level="component">
                <LogsViewer />
              </DashboardErrorBoundary>
            </TabsContent>
            
            <TabsContent value="queue">
              <DashboardErrorBoundary name="QueueManagement" level="component">
                <QueueManagement />
              </DashboardErrorBoundary>
            </TabsContent>
          </Tabs>
        </TabsContent>

        <TabsContent value="workspace" className="space-y-6">
          <DashboardErrorBoundary name="WorkspaceManager" level="component">
            <WorkspaceManager 
              onLoad={(item) => {
                console.log('Loading workspace item:', item);
                // Handle loading workspace item
              }}
              currentData={{
                code: '// Current code',
                prompt: 'Current prompt',
                settings: modelConfig
              }}
            />
          </DashboardErrorBoundary>
        </TabsContent>

        <TabsContent value="notifications" className="space-y-6">
          <DashboardErrorBoundary name="NotificationCenter" level="component">
            <NotificationCenter />
          </DashboardErrorBoundary>
        </TabsContent>
      </Tabs>
    </div>
  );

  // Mobile layout using the enhanced mobile components
  if (isMobile || isTablet) {
    return (
      <DashboardErrorBoundary name="MobileDashboard" level="page">
        <MobileDashboardLayout
          performanceWidget={
            <DashboardErrorBoundary name="PerformanceMetrics" level="widget">
              <PerformanceMetricsWidget />
            </DashboardErrorBoundary>
          }
          costWidget={
            <DashboardErrorBoundary name="CostTracking" level="widget">
              <CostTrackingWidget />
            </DashboardErrorBoundary>
          }
          usageWidget={
            <DashboardErrorBoundary name="UsageStatistics" level="widget">
              <UsageStatisticsPanel />
            </DashboardErrorBoundary>
          }
          healthWidget={
            <DashboardErrorBoundary name="SystemHealth" level="widget">
              <SystemHealthIndicators />
            </DashboardErrorBoundary>
          }
          activityWidget={
            <DashboardErrorBoundary name="RecentActivity" level="widget">
              <RecentActivityTimeline />
            </DashboardErrorBoundary>
          }
          notificationWidget={
            <DashboardErrorBoundary name="NotificationCenter" level="widget">
              <NotificationCenter />
            </DashboardErrorBoundary>
          }
          settingsWidget={
            <DashboardErrorBoundary name="QuickSettings" level="widget">
              <QuickSettingsPanel 
                onSettingsChange={setModelConfig}
                initialSettings={modelConfig}
              />
            </DashboardErrorBoundary>
          }
          className={className}
        />
      </DashboardErrorBoundary>
    );
  }

  return <DesktopDashboard />;
};

// Export individual components for use elsewhere
export {
  PerformanceMetricsWidget,
  CostTrackingWidget,
  UsageStatisticsPanel,
  SystemHealthIndicators,
  RecentActivityTimeline,
  QuickSettingsPanel,
  NotificationCenter,
  AdminPanel,
  ApiKeyManagement,
  LogsViewer,
  QueueManagement,
  OnboardingWizard,
  WorkspaceManager,
  DashboardErrorBoundary
};

export default DashboardIntegration;
